/* European */
:lang("en"), :lang("eng"),	/* English */		/* LATIN */
:lang("eu"), :lang("eus"),	/* Basque */
:lang("br"), :lang("bre"),	/* Breton */
:lang("ca"), :lang("cat"),	/* Catalan  */
:lang("co"), :lang("cos"),	/* Corsican */
:lang("kw"), :lang("cor"),	/* Cornish */
:lang("cy"), :lang("cym"),	/* Welsh */
:lang("cs"), :lang("ces"),	/* Czech */
:lang("da"), :lang("dan"),	/* Danish */
:lang("nl"), :lang("nld"),	/* Dutch */
:lang("et"), :lang("est"),	/* Estonian */
:lang("fo"), :lang("fao"),	/* Faroese */
:lang("fi"), :lang("fin"),	/* Finnish */
:lang("fr"), :lang("fra"),	/* French */
:lang("gl"), :lang("glg"),	/* Galician */
:lang("de"), :lang("deu"),	/* German */
:lang("ht"), :lang("hat"),	/* Haitian; Haitian Creole */
:lang("hu"), :lang("hun"),	/* Hungarian */
:lang("ga"), :lang("gle"),	/* Irish */
:lang("is"), :lang("isl"),	/* Icelandic */
:lang("it"), :lang("ita"),	/* Italian */
:lang("la"), :lang("lat"),	/* Latin */
:lang("lb"), :lang("ltz"),	/* Luxembourgish */
:lang("li"), :lang("lim"),	/* Limburgish */
:lang("lt"), :lang("lit"),	/* Lithuanian */
:lang("lv"), :lang("lav"),	/* Latvian */
:lang("gv"), :lang("glv"),	/* Manx */
:lang("no"), :lang("nor"),	/* Norwegian */
:lang("nn"), :lang("nno"),	/* Norwegian Nynorsk */
:lang("nb"), :lang("nob"),	/* Norwegian Bokmal */
:lang("pl"), :lang("pol"),	/* Polish */
:lang("pt"), :lang("por"),	/* Portuguese */
:lang("ro"), :lang("ron"),	/* Romanian */
:lang("gd"), :lang("gla"),	/* Scottish Gaelic  */
:lang("es"), :lang("spa"),	/* Spanish */
:lang("sv"), :lang("swe"),	/* Swedish */
:lang("sl"), :lang("slv"),	/* Slovene */
:lang("sk"), :lang("slk")  {	/* Slovak */
	font-family: dejavusanscondensed;
}


:lang("el"), :lang("ell")  {	/* GREEK */
	font-family: dejavusanscondensed;
}

:lang("ru"), :lang("rus"),	/* Russian */	/* CYRILLIC */
:lang("ab"), :lang("abk"),	/* Abkhaz */
:lang("av"), :lang("ava"),	/* Avaric */
:lang("ba"), :lang("bak"),	/* Bashkir */
:lang("be"), :lang("bel"),	/* Belarusian */
:lang("bg"), :lang("bul"),	/* Bulgarian */
:lang("ce"), :lang("che"),	/* Chechen */
:lang("cv"), :lang("chv"),	/* Chuvash */
:lang("kk"), :lang("kaz"),	/* Kazakh */
:lang("kv"), :lang("kom"),	/* Komi */
:lang("ky"), :lang("kir"),	/* Kyrgyz */
:lang("mk"), :lang("mkd"),	/* Macedonian */
:lang("cu"), :lang("chu"),	/* Old Church Slavonic */
:lang("os"), :lang("oss"),	/* Ossetian */
:lang("sr"), :lang("srp"),	/* Serbian */
:lang("tg"), :lang("tgk"),	/* Tajik */
:lang("tt"), :lang("tat"),	/* Tatar */
:lang("tk"), :lang("tuk"),	/* Turkmen */
:lang("uk"), :lang("ukr")  {	/* Ukrainian */
	font-family: dejavusanscondensed;
}



:lang("hy"), :lang("hye")  {	/* ARMENIAN */
	font-family: dejavusanscondensed;
}
:lang("ka"), :lang("kat")  {	/* GEORGIAN */
	font-family: dejavusanscondensed;
}
:lang("cop") {		/* COPTIC */
	font-family: quivira;
}
:lang("got") {		/* GOTHIC */
	font-family: freeserif;
}
:lang("und-Latn") {	/* LATIN */
	font-family: dejavusanscondensed;
}
:lang("und-Cyrl") {	/* CYRILLIC */
	font-family: dejavusanscondensed;
}
:lang("und-Cprt") {	/* CYPRIOT */
	font-family: aegean;
}
:lang("und-Glag") {	/* GLAGOLITIC */
	font-family: mph2bdamase;
}
:lang("und-Linb") {	/* LINEAR_B */
	font-family: aegean;
}
:lang("und-Ogam") {	/* OGHAM */
	font-family: dejavusanscondensed;
}
:lang("und-Ital") {	/* OLD_ITALIC */
	font-family: aegean;
}
:lang("und-Runr") {	/* RUNIC */
	font-family: sun-exta;
}
:lang("und-Shaw") {	/* SHAVIAN */
	font-family: mph2bdamase;
}




/* African */
:lang("am"), :lang("amh")  {	/* Amharic ETHIOPIC */
	font-family: abyssinicasil;
}
:lang("ti"), :lang("tir")  {	/* Tigrinya ETHIOPIC */
	font-family: abyssinicasil;
}
:lang("nqo") {		/* NKO */
	font-family: dejavusanscondensed;
}
:lang("vai") {		/* VAI */
	font-family: freesans;
}
:lang("und-Egyp") {	/* EGYPTIAN_HIEROGLYPHS */
	font-family: aegyptus;
}
:lang("und-Ethi") {	/* ETHIOPIC */
	font-family: abyssinicasil;
}
:lang("und-Osma") {	/* OSMANYA */
	font-family: mph2bdamase;
}
:lang("und-Tfng") {	/* TIFINAGH */
	font-family: dejavusanscondensed;
}





/* Middle Eastern */
:lang("ar"), :lang("ara")  {	/* Arabic */
	font-family: xbriyaz;
}
:lang("fa"), :lang("fas")  {	/* Persian (Farsi) */
	font-family: xbriyaz;  
}
:lang("ku"), :lang("kur")  {	/* Kurdish */
	font-family: xbriyaz;  
}
:lang("ps"), :lang("pus")  {	/* Pashto */
	font-family: xbriyaz; 
}
:lang("ur"), :lang("urd")  {	/* Urdu */
	font-family: xbriyaz; 
}
:lang("he"), :lang("heb")  {	/* HEBREW */
	font-family: taameydavidclm;
}
:lang("yi"), :lang("yid")  {	/* Yiddish */
	font-family: taameydavidclm;
}
:lang("syr") {		/* SYRIAC */
	font-family: estrangeloedessa;
}

:lang("xcr") {		/* CARIAN */
	font-family: aegean;
}
:lang("xlc") {		/* LYCIAN */
	font-family: aegean;
}
:lang("xld") {		/* LYDIAN */
	font-family: aegean;
}
:lang("phn") {		/* PHOENICIAN */
	font-family: aegean;
}
:lang("uga") {		/* UGARITIC */
	font-family: aegean;
}

:lang("und-Arab") {	/* ARABIC */
	font-family: xbriyaz;
}
:lang("und-Xsux") {	/* CUNEIFORM */
	font-family: akkadian;
}




/* Central Asian */
:lang("bo"), :lang("bod"),	/* TIBETAN */
:lang("dz"), :lang("dzo")  {	/* Dzongkha */
	font-family: jomolhari;
}



/* South Asian */
:lang("as"), :lang("asm")  {	/* Assamese */
	font-family: freeserif;
}
:lang("bn"), :lang("ben")  {	/* BENGALI; Bangla */
	font-family: freeserif;
}
:lang("ks"), :lang("kas")  {	/* Kashmiri */
	font-family: freeserif;
}
:lang("hi"), :lang("hin"),	/* Hindi	DEVANAGARI */
:lang("bh"), :lang("bih"),	/* Bihari (Bhojpuri, Magahi, and Maithili) */
:lang("sa"), :lang("san")  {	/* Sanskrit */
	font-family: freeserif;
}
:lang("gu"), :lang("guj")  {	/* Gujarati */
	font-family: freeserif;
}
:lang("pa"), :lang("pan")  {	/* Panjabi, Punjabi GURMUKHI */
	font-family: freeserif;
}
:lang("kn"), :lang("kan")  {	/* Kannada */
	font-family: lohitkannada;
}
:lang("mr"), :lang("mar")  {	/* Marathi */
	font-family: freeserif;
}
:lang("ml"), :lang("mal")  {	/* MALAYALAM */
	font-family: freeserif;
}
:lang("ne"), :lang("nep")  {	/* Nepali */
	font-family: freeserif;
}
:lang("or"), :lang("ori")  {	/* ORIYA */
	font-family: freeserif;
}
:lang("si"), :lang("sin")  {	/* SINHALA */
	font-family: kaputaunicode;
}
:lang("ta"), :lang("tam")  {	/* TAMIL */
	font-family: freeserif;
}
:lang("te"), :lang("tel")  {	/* TELUGU */
	font-family: pothana2000;
}
:lang("sd"), :lang("snd")  {	/* Sindhi */
	font-family: lateef;
}
:lang("sd-IN") {		/* Sindhi */
	font-family: freeserif;
}
:lang("sd-PK") {		/* Sindhi */
	font-family: lateef;
}

:lang("lif") {		/* LIMBU */
	font-family: sun-exta;
}
:lang("syl") {		/* SYLOTI_NAGRI */
	font-family: mph2bdamase;
}
:lang("dv"), :lang("div")  {	/* Divehi; Maldivian  THAANA */
	font-family: freeserif;
}
:lang("und-Khar") {	/* KHAROSHTHI */
	font-family: mph2bdamase;
}
:lang("und-Mtei") {	/* MEETEI_MAYEK */
	font-family: eeyekunicode;
}




/* South East Asian */
:lang("km"), :lang("khm")  {	/* KHMER */
	font-family: khmeros;
}
:lang("lo"), :lang("lao")  {	/* LAO */
	font-family: dhyana;
}
:lang("my"), :lang("mya")  {	/* MYANMAR Burmese */
	font-family: tharlon;	/* zawgyi-one is non-unicode compliant but in wide usage ; ayar is also not strictly compliant */
}
:lang("th"), :lang("tha")  {	/* THAI */
	font-family: garuda;
}


:lang("vi"), :lang("vie")  {	/* Vietnamese */
	font-family: dejavusanscondensed; 
}


:lang("bug") {		/* BUGINESE */
	font-family: freeserif;
}
:lang("su") {		/* SUNDANESE */
	font-family: sundaneseunicode;
}
:lang("tdd") {		/* TAI_LE */
	font-family: tharlon;
}
:lang("blt") {		/* TAI_VIET */
	font-family: taiheritagepro;
}
:lang("und-Kali") {	/* KAYAH_LI */
	font-family: freemono;
}
:lang("und-Lana") {	/* TAI_THAM */
	font-family: lannaalif;
}
:lang("und-Talu") {	/* NEW_TAI_LUE */
	font-family: daibannasilbook;
}



/* Phillipine */
:lang("bku") {		/* BUHID */
	font-family: quivira;
}
:lang("hnn") {		/* HANUNOO */
	font-family: quivira;
}
:lang("tl") {		/* TAGALOG */
	font-family: quivira;
}
:lang("tbw") {		/* TAGBANWA */
	font-family: quivira;
}




/* East Asian */
:lang("zh"), :lang("zho")  {	/* Chinese */
	font-family: sun-exta, gb;
}
:lang("zh-HK") {		/* Hong Kong */
	font-family: sun-exta, big5;
}
:lang("zh-TW") {		/* Taiwan */
	font-family: sun-exta, big5;
}
:lang("zh-CN") {		/* Chinese */
	font-family: sun-exta, gb;
}
:lang("ko"), :lang("kor")  {	/* HANGUL Korean */
	font-family: unbatang, uhc;
}
:lang("ja"), :lang("jpn")  {	/* Japanese HIRAGANA KATAKANA */
	font-family: sun-exta, sjis;
}
:lang("lis") {		/* LISU */
	font-family: quivira;
}
:lang("und-Hans") {	/* HAN (SIMPLIFIED) */
	font-family: sun-exta, gb;
}
:lang("und-Bopo") {	/* BOPOMOFO */
	font-family: sun-exta;
}
:lang("ii"), :lang("iii"),	/* Nuosu; Yi */
:lang("und-Yiii") {	/* YI */
	font-family: sun-exta;
}



/* American */
:lang("chr") {		/* CHEROKEE */
	font-family: aboriginalsans;
}
:lang("oj"), :lang("oji"),	/* Ojibwe; Chippewa */
:lang("iu"), :lang("iku"),	/* Inuktitut */
:lang("cr"), :lang("cre")  {	/* Cree CANADIAN_ABORIGINAL */
	font-family: aboriginalsans;
}
:lang("und-Dsrt") {	/* DESERET */
	font-family: mph2bdamase;
}




/*  Other */
:lang("und-Brai") {	/* BRAILLE */
	font-family: dejavusans;
}
